import React, { useEffect, useState, useRef, MouseEvent, useCallback, Suspense } from 'react';
import { useParams, useLocation } from 'react-router-dom';
import { useLanguageNavigate } from '../hooks/useLanguageNavigate';
import { useTranslation } from 'react-i18next';
import { motion } from 'framer-motion';
// 移除ReactMarkdown组件
import SEO from '../components/SEO';
import Footer from '../components/Footer';
import LandingBackground from '../components/LandingBackground';
// @ts-ignore - 暂时忽略模块解析错误，blogPosts确实存在但TypeScript可能没有正确识别
import { blogPostsByLang, allBlogPosts } from '../data/blogPosts';
import { getFontClass } from '../utils/fontUtils';
import { useTheme } from '../contexts/ThemeContext';
import { TAROT_CARDS } from '../data/tarot-cards';
// 导入新创建的API服务
import { getBlogReading } from '../services/blogReadingService';
import { checkFingerprintBlogCombination } from '../services/fingerprintService';
import BrowserFingerprint from '../components/BrowserFingerprint';
import SpotlightCard from '../blocks/Components/SpotlightCard/SpotlightCard';
import { getImageUrl } from '../utils/cdnImageUrl';
import CdnLazyImage from '../components/CdnLazyImage';
import { BlogPost } from '../data/types';
import { getSEOConfig } from '../lib/SEOConfig';

// 导入塔罗牌抽取功能相关组件
import FloatingHint from '../components/TarotCardSelection/FloatingHint';
import SlideControls from '../components/TarotCardSelection/SlideControls';
import NumberInput from '../components/TarotCardSelection/NumberInput';
import CardDeck from '../components/TarotCardSelection/CardDeck';
import CustomActionButton from '../components/TarotCardSelection/CustomActionButton';



// 卡牌位置的接口定义
interface CardPosition {
  card: number | undefined;
  position: string;
  orientation: boolean;
}

// 选中卡牌组件的Props接口
interface SelectedCardsProps {
  positions: CardPosition[];
  cardImages: Map<number, string>;
  currentLanguage: string;
  isDark: boolean;
  t: (key: string) => string;
}

// SelectedCards组件实现
const SelectedCards: React.FC<SelectedCardsProps> = ({
  positions,
  cardImages,
  currentLanguage,
  isDark,
  t
}) => {
  const cardCount = positions.length;
  
  return (
    <div className="flex flex-wrap justify-center">
      {positions.map((item, index) => {
        const { card: selectedCard, position, orientation } = item;
        
        // 根据卡牌数量决定单个卡牌的宽度
        const getCardWidth = () => {
          if (cardCount <= 3) {
            return "w-[110px] sm:w-[130px] md:w-[150px]"; // 少量卡牌时可以显示得大一些
          } else if (cardCount <= 6) {
            return "w-[100px] sm:w-[120px] md:w-[140px]"; // 中等数量卡牌
          } else {
            return "w-[90px] sm:w-[100px] md:w-[120px]";  // 大量卡牌时适当减小
          }
        };
        
        return (
          <motion.div 
            key={index}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1, duration: 0.5 }}
            className={`${getCardWidth()} m-2 sm:m-3 md:m-4 flex flex-col items-center`}
          >
            {selectedCard !== undefined ? (
              <>
                <div className="relative w-full">
                  <div className={`w-full aspect-[2/3] rounded-lg overflow-hidden border-0 transition-all duration-300 
                    ${isDark ? 'shadow-black/30' : 'shadow-gray-300/70'} shadow-md
                    flex items-center justify-center bg-transparent`}
                  >
                    <motion.div 
                      className="h-full w-full flex items-center justify-center"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ duration: 0.3 }}
                      style={{ 
                        transform: orientation ? 'rotate(180deg)' : 'rotate(0deg)'
                      }}
                    >
                      <CdnLazyImage 
                        src={cardImages.get(selectedCard) || ''}
                        alt={currentLanguage === 'en' ? TAROT_CARDS[selectedCard]?.nameEn.replace(/_/g, ' ') : TAROT_CARDS[selectedCard]?.name || t('reading.shuffle.tarot_card')}
                        className="h-full w-full object-contain"
                        style={{ imageRendering: 'crisp-edges' }}
                        draggable="false"
                      />
                    </motion.div>
                  </div>
                </div>
                
                <div className="text-center mt-2 space-y-0.5 w-full">
                  <h3 className="text-xs sm:text-sm font-medium text-white font-sans japanese truncate">
                    {currentLanguage === 'en' ? TAROT_CARDS[selectedCard]?.nameEn.replace(/_/g, ' ') : TAROT_CARDS[selectedCard]?.name || t('reading.shuffle.tarot_card')}
                  </h3>
                  <p className="text-xs sm:text-sm text-purple-500 font-sans japanese">
                    {t(orientation ? 'reading.result.reversed' : 'reading.result.upright')}
                  </p>
                  <p className="text-xs sm:text-sm text-gray-400 font-sans japanese truncate">
                    {position}
                  </p>
                </div>
              </>
            ) : (
              <>
                <div className="relative w-full">
                  <div className={`w-full aspect-[2/3] flex items-center justify-center border-0 rounded-lg 
                    ${isDark ? 'shadow-black/30 group-hover:border-purple-500/30' : 'shadow-gray-300/70 group-hover:border-purple-400/50'} shadow-md
                    transition-all duration-300 bg-transparent`}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className={`h-6 w-6 sm:h-8 sm:w-8 ${isDark ? 'text-gray-600/80' : 'text-gray-400/80'}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 4v16m8-8H4" />
                    </svg>
                  </div>
                </div>
                
                <div className="text-center mt-2 space-y-0.5 w-full">
                  <h3 className={`text-xs sm:text-sm font-medium ${isDark ? 'text-white/60' : 'text-gray-600/80'} font-sans japanese truncate`}>{t('reading.shuffle.pending_card')}</h3>
                  <p className={`text-xs sm:text-sm ${isDark ? 'text-gray-400' : 'text-gray-500'} font-sans japanese truncate`}>{position}</p>
                </div>
              </>
            )}
          </motion.div>
        );
      })}
    </div>
  );
};

// 自定义的SpreadContainer组件
const CustomSpreadContainer: React.FC<{
  isDark: boolean;
  t: (key: string, options?: any) => string;
  translatedSpreadInfo: any;
  requiredCards: number;
  flippedCards: number[];
  cardOrientations: Map<number, boolean>;
  cardImages: Map<number, string>;
  currentLanguage: string;
  handleFormSpread: () => void;
  hasExistingReading: boolean;
  isLoadingReading: boolean;
  hasCompletedReading: boolean; // 添加这一行
}> = ({
  isDark,
  t,
  translatedSpreadInfo,
  requiredCards,
  flippedCards,
  cardOrientations,
  cardImages,
  currentLanguage,
  handleFormSpread,
  hasExistingReading = false,
  isLoadingReading = false,
  hasCompletedReading = false // 添加这一行
}) => {
  // 准备位置数据
  const positions = Array.from({ length: requiredCards }).map((_, index) => {
    const position = translatedSpreadInfo?.positions?.[index] || `位置 ${index + 1}`;
    const selectedCard = flippedCards[index];
    const orientation = cardOrientations.get(selectedCard) || false;
    
    return {
      card: selectedCard,
      position,
      orientation
    };
  });
  
  // 根据卡牌数量决定内边距大小
  const getPadding = () => {
    const cardCount = requiredCards;
    
    if (cardCount <= 3) {
      return "p-4 xs:p-5 sm:p-6 md:p-10";
    } else if (cardCount <= 5) {
      return "p-3 xs:p-4 sm:p-5 md:p-8";
    } else {
      return "p-2 xs:p-3 sm:p-4 md:p-6";
    }
  };
  
  return (
    <motion.div 
      className={`${isDark ? 'bg-gradient-to-b from-gray-800/40 to-gray-900/40 backdrop-blur-md border-purple-500/10' : 'bg-gradient-to-b from-gray-100/80 to-white/80 backdrop-blur-md border-purple-300/20'} rounded-xl sm:rounded-2xl overflow-hidden shadow-lg sm:shadow-2xl border mt-4 sm:mt-6 mb-6 sm:mb-8`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.1 }}
    >
      {/* Header with spread name */}
      <div className={`py-3 sm:py-5 px-4 sm:px-6 md:px-8 ${isDark ? 'border-gray-700/30' : 'border-gray-300/50'} border-b`}>
        <h2 className="main-title text-base sm:text-lg md:text-xl mb-0.5 sm:mb-1 text-center">
          {translatedSpreadInfo?.name || t('reading.shuffle.form_spread')}
        </h2>
        {translatedSpreadInfo?.description && (
          <p className={`text-xs sm:text-sm ${isDark ? 'text-gray-300' : 'text-gray-600'} text-center mx-auto max-w-2xl line-clamp-2 sm:line-clamp-none`}>
            {translatedSpreadInfo.description}
          </p>
        )}
      </div>
      
      {/* Cards container */}
      <div className={getPadding()}>
        <SelectedCards 
          positions={positions}
          cardImages={cardImages}
          currentLanguage={currentLanguage}
          isDark={isDark}
          t={t}
        />
      </div>
      
      {/* Action button */}
      <div className="px-4 pb-4 sm:px-6 sm:pb-6 md:px-8 md:pb-8">
                      <CustomActionButton 
                onClick={handleFormSpread}
                isEnabled={flippedCards.length === requiredCards}
                isDark={isDark}
                t={t}
                requiredCards={requiredCards}
                selectedCount={flippedCards.length}
                hasExistingReading={hasExistingReading}
                isLoading={isLoadingReading}
                hasCompletedReading={hasCompletedReading} // 添加这一行
              />
      </div>
    </motion.div>
  );
};

// 添加默认牌阵
const defaultSpread = {
  id: 'three-card',
  name: '三张牌阵',
  description: '过去、现在、未来',
  cardCount: 3,
  positions: ['过去', '现在', '未来'],
  layout: 'horizontal'
};

// 相关文章组件
const RelatedArticles: React.FC<{
  currentSlug: string;
  theme: string;
  language: string;
  navigate: Function;
  i18n: any; // 添加i18n参数
}> = ({ currentSlug, theme, language, navigate, i18n }) => {
  // 获取相关文章
  const getRelatedPosts = () => {
    const langPosts = blogPostsByLang[language] || [];
    
    // 如果当前语言没有足够文章，尝试回退到简体中文
    if (langPosts.length <= 1 && language !== 'zh-CN') {
      const posts = (blogPostsByLang['zh-CN'] || []).filter(post => post.slug !== currentSlug);
      return posts.slice(0, 3); // 最多3篇相关文章
    }
    
    // 移除当前文章，返回其他文章
    return langPosts.filter(post => post.slug !== currentSlug).slice(0, 3);
  };
  
  const relatedPosts = getRelatedPosts();
  
  if (relatedPosts.length === 0) return null;
  
  return (
    <div className="mt-10">
      <h2 className={`text-xl font-bold mb-6 ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
        {language === 'en' ? 'Related Articles' : 
         language === 'ja' ? '関連記事' : 
         language === 'zh-TW' ? '相關文章' : '相关文章'}
      </h2>
      
      <div className="flex flex-col space-y-4">
        {relatedPosts.map((post: BlogPost) => (
          <div 
            key={post.slug}
            onClick={() => {
              // 根据文章类别和useNewUrlFormat属性决定使用哪种URL格式
              let url = `/blog/${post.slug}`;
              
              if (post.useNewUrlFormat) {
                if (post.category === '星座特质') {
                  url = `/zodiac-traits/${post.slug}`;
                } else if (post.category === '塔罗指南') {
                  url = `/tarot-guide/${post.slug}`;
                } else if (post.category === '综合占卜') {
                  url = `/general-divination/${post.slug}`;
                }
              }
              
              navigate(url);
            }}
            className={`cursor-pointer rounded-lg overflow-hidden border w-full flex ${theme === 'dark' ? 'border-gray-700' : 'border-gray-200'} shadow-sm`}
          >
            <div className="w-1/3 flex-shrink-0 overflow-hidden">
              <CdnLazyImage 
                src={post.coverImage} 
                alt={`Blog cover - ${post.title}`}
                className="w-full h-full object-cover"
                style={{ height: '100%', objectFit: 'cover' }}
              />
            </div>
            
            <div className="p-4 w-2/3 flex flex-col justify-center">
              <h3 className={`font-bold mb-2 line-clamp-2 ${theme === 'dark' ? 'text-white' : 'text-gray-800'}`}>
                {post.title}
              </h3>
              <p className={`text-sm line-clamp-2 ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
                {post.description || getSEOConfig(`/blog/${post.slug}`, i18n).description}
              </p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

const BlogDetail: React.FC = () => {
  const { slug } = useParams<{ slug: string }>();
  const { navigate } = useLanguageNavigate();
  const { i18n } = useTranslation();
  const { theme, toggleTheme } = useTheme();
  const contentRef = useRef<HTMLDivElement>(null);
  const [headings, setHeadings] = useState<{id: string; text: string; level: number}[]>([]);
  const [activeId, setActiveId] = useState<string>('');
  const [isMobile, setIsMobile] = useState<boolean>(false);
  
  // 获取当前路径，用于判断是从哪个URL格式访问的
  const location = useLocation();
  const isZodiacTraitsPath = location.pathname.includes('/zodiac-traits/');
  const isTarotGuidePath = location.pathname.includes('/tarot-guide/');
  const isGeneralDivinationPath = location.pathname.includes('/general-divination/');
  
  // 获取当前访问的分类路径
  const getCategoryPath = () => {
    if (isZodiacTraitsPath) return 'zodiac-traits';
    if (isTarotGuidePath) return 'tarot-guide';
    if (isGeneralDivinationPath) return 'general-divination';
    return 'blog';
  };
  
  // 使用useRef存储最新的theme和toggleTheme函数引用
  const themeRef = useRef(theme);
  const toggleThemeRef = useRef(toggleTheme);
  
  // 添加塔罗牌相关状态
  const { t } = useTranslation();
  const isDark = theme === 'dark';
  const currentLanguage = i18n.language;
  const scrollRef = useRef<HTMLDivElement>(null);

  // 卡牌相关状态
  const [cardImages, setCardImages] = useState<Map<number, string>>(new Map());
  const [flippedCards, setFlippedCards] = useState<number[]>([]);
  const [processingCards, setProcessingCards] = useState<Set<number>>(new Set());
  const [cardOrientations, setCardOrientations] = useState<Map<number, boolean>>(new Map());
  const [numberInputValue, setNumberInputValue] = useState('');
  const [numberInputError, setNumberInputError] = useState('');
  
  // 添加状态相关refs
  const numberInputErrorTimeoutRef = useRef<NodeJS.Timeout>();
  
  // 卡背图片设置
  const [cardBackImage] = useState<string>(getImageUrl('/images-optimized/back/001.webp'));
  
  // 添加拖动状态
  const [isDragging, setIsDragging] = useState(false);
  const [startX, setStartX] = useState(0);
  const [scrollLeft, setScrollLeft] = useState(0);
  const [dragActive, setDragActive] = useState(false);

  // 选择模式默认为滑动
  const [selectionMode, setSelectionMode] = useState<'slide' | 'number'>('slide');
  
  // 添加随机排列的牌索引状态
  const [randomCardIndices, setRandomCardIndices] = useState<number[]>([]);
  
  // 添加状态表示用户是否已完成抽牌
  const [hasCompletedReading, setHasCompletedReading] = useState<boolean>(false);

  // 添加指纹状态
  const [fingerprint, setFingerprint] = useState<string>('');
  // 添加历史记录状态
  const [hasExistingReading, setHasExistingReading] = useState<boolean>(false);
  const [historicalReading, setHistoricalReading] = useState<string>('');
  // 添加检查历史标志，防止无限循环
  const [hasCheckedHistory, setHasCheckedHistory] = useState<boolean>(false);
  
  // 处理指纹就绪事件
  const handleFingerprintReady = useCallback(async (visitorId: string) => {
    setFingerprint(visitorId);
    
    // 如果有博客ID，检查是否存在历史记录
    if (slug && visitorId && !hasCheckedHistory) {
      try {
        // 设置标志，防止重复检查
        setHasCheckedHistory(true);
        
        const checkResult = await checkFingerprintBlogCombination(visitorId, slug);
        
        // 如果存在历史记录
        if (checkResult.exists && checkResult.record) {
          
          setHasExistingReading(true);
          setHistoricalReading(checkResult.record.response_content);
          setHasCompletedReading(true);
          
          // 解析历史记录中的卡牌数据并显示
          try {
            if (checkResult.record.selected_cards) {
              const selectedCardsData = JSON.parse(checkResult.record.selected_cards);
              
              if (Array.isArray(selectedCardsData) && selectedCardsData.length > 0) {
                // 设置已翻开的牌
                const cardIds = selectedCardsData.map(card => card.id);
                setFlippedCards(cardIds);
              }
            }
          } catch (parseError) {
            // console.error('解析历史卡牌数据失败:', parseError);
          }
        }
      } catch (error) {
        // console.error('检查历史记录失败:', error);
      }
    }
  }, [slug, hasCheckedHistory]);

  // 获取当前语言的博客文章
  const getCurrentLanguagePosts = () => {
    // 尝试获取当前语言的文章
    const langPosts = blogPostsByLang[i18n.language] || [];
    
    // 如果当前语言没有文章，尝试回退到简体中文
    if (langPosts.length === 0 && i18n.language !== 'zh-CN') {
      return blogPostsByLang['zh-CN'] || [];
    }
    
    return langPosts;
  };
  
  // 获取当前文章
  const currentPost = getCurrentLanguagePosts().find((post: BlogPost) => post.slug === slug) || 
                      allBlogPosts.find((post: BlogPost) => post.slug === slug);
  
  // 使用当前文章的牌阵或默认牌阵
  const [spread, setSpread] = useState<any>(
    currentPost?.tarotSpread || defaultSpread
  );
  
  // 获取所需的卡牌数量
  const requiredCards = spread?.cardCount ?? 3;

  // 当currentPost更新时更新spread
  useEffect(() => {
    if (currentPost?.tarotSpread) {
      setSpread(currentPost.tarotSpread);
    } else {
      setSpread(defaultSpread);
    }
  }, [currentPost]);

  // 获取翻译后的牌阵信息
  const getTranslatedSpreadInfo = () => {
    if (!spread) return null;
    
    const spreadId = spread.id.replace(/-/g, '_');
    if (Object.keys(t('spreads', { returnObjects: true })).includes(spreadId)) {
      return {
        name: t(`spreads.${spreadId}.name`),
        description: t(`spreads.${spreadId}.description`),
        positions: spread.positions.map((_: string, index: number) => {
          const positionKeys = Object.keys(t(`spreads.${spreadId}.positions`, { returnObjects: true }));
          return t(`spreads.${spreadId}.positions.${positionKeys[index]}`);
        })
      };
    }
    
    return {
      name: spread.name,
      description: spread.description,
      positions: spread.positions
    };
  };

  const translatedSpreadInfo = getTranslatedSpreadInfo();
  
  // 更新引用值
  useEffect(() => {
    themeRef.current = theme;
    toggleThemeRef.current = toggleTheme;
  }, [theme, toggleTheme]);
  
  // 检测移动设备
  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768); // 小于768px视为移动设备
    };
    
    // 初始检测
    checkIfMobile();
    
    // 监听窗口大小变化
    window.addEventListener('resize', checkIfMobile);
    
    // 清理函数
    return () => window.removeEventListener('resize', checkIfMobile);
  }, []);
  
  // 初始化随机排序的卡牌索引
  useEffect(() => {
    // 创建一个包含0-77的数组
    const indices = Array.from({ length: 78 }, (_, i) => i);
    // 使用Fisher-Yates洗牌算法打乱数组
    for (let i = indices.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [indices[i], indices[j]] = [indices[j], indices[i]];
    }
    setRandomCardIndices(indices);
  }, []);

  // 加载卡牌图片
  useEffect(() => {
    const loadCardImages = () => {
      const newCardImageMap = new Map<number, string>();
      TAROT_CARDS.forEach((card, cardId) => {
        const imageName = `${card.nameEn}.webp`;
        const imagePath = `/images-optimized/tarot/${imageName}`;
        // 使用CDN图片URL处理
        const processedImagePath = getImageUrl(imagePath);
        newCardImageMap.set(cardId, processedImagePath);
      });
      setCardImages(newCardImageMap);
    };

    loadCardImages();
  }, []);

  // 定义动画持续时间常量
  const ANIMATION_DURATION = {
    flip: window.innerWidth < 640 ? 0.2 : 0.3,
    scrollDelay: window.innerWidth < 640 ? 250 : 350,
    resetProcessing: window.innerWidth < 640 ? 200 : 300
  };

  // 处理卡牌翻转
  const handleCardFlip = (cardId: number) => {
    // 如果卡牌正在处理中，直接返回
    if (processingCards.has(cardId)) return;
    
    // 如果卡牌已经翻开，或者已经选择了足够数量的卡牌，则直接返回
    if (flippedCards.includes(cardId) || flippedCards.length >= requiredCards) return;
    
    // 添加到处理中的卡片
    setProcessingCards(prev => new Set([...prev, cardId]));
    
    // 检测是否为移动设备
    const isMobile = window.innerWidth < 640;
    
    // 添加滚动逻辑
    if (scrollRef.current) {
      // 找到对应卡牌的DOM元素
      const cardElements = scrollRef.current.querySelectorAll('.card-wrapper');
      const cardIndex = randomCardIndices.length === 78 
        ? randomCardIndices.findIndex(id => id === cardId) 
        : cardId;
      
      if (cardIndex >= 0 && cardIndex < cardElements.length) {
        const cardElement = cardElements[cardIndex];
        
        // 计算卡牌到视口左侧的距离
        const cardLeft = cardElement.getBoundingClientRect().left;
        const containerLeft = scrollRef.current.getBoundingClientRect().left;
        const offset = cardLeft - containerLeft;
        
        // 将卡牌滚动到容器中间位置
        const centerOffset = isMobile ? scrollRef.current.clientWidth / 3 : scrollRef.current.clientWidth / 2;
        const scrollTo = scrollRef.current.scrollLeft + offset - centerOffset + (cardElement.clientWidth / 2);
        
        // 滚动到卡牌位置，使用平滑滚动效果
        try {
          // 保存当前页面滚动位置
          const pageScrollY = window.scrollY;
          
          // 滚动卡牌容器，使用平滑滚动
          scrollRef.current.scrollTo({
            left: scrollTo,
            behavior: 'smooth'
          });
          
          // 确保页面滚动位置不变
          setTimeout(() => {
            if (window.scrollY !== pageScrollY) {
              window.scrollTo({
                top: pageScrollY,
                behavior: 'auto'
              });
            }
          }, 10);
          
          // 等待滚动完成后再翻牌，增加延迟时间以适应平滑滚动
          setTimeout(() => {
            performCardFlip(cardId);
          }, ANIMATION_DURATION.scrollDelay + 100);
        } catch (error) {
          // console.error('Card scroll error:', error);
          performCardFlip(cardId);
        }
      } else {
        performCardFlip(cardId);
      }
    } else {
      performCardFlip(cardId);
    }
  };

  // 执行卡牌翻转逻辑
  const performCardFlip = (cardId: number) => {
    // 随机确定卡牌正逆位（50%概率逆位）
    const isReversed = Math.random() < 0.5;
    
    // 批量更新状态
    setCardOrientations(prev => {
      const newOrientations = new Map(prev);
      newOrientations.set(cardId, isReversed);
      return newOrientations;
    });
    
    // 添加到已翻开的牌中
    setFlippedCards(prev => {
      // 再次检查是否超出限制
      if (prev.length >= requiredCards) return prev;
      return [...prev, cardId];
    });
    
    // 卡片动画完成后，从处理中的卡片移除
    setTimeout(() => {
      setProcessingCards(prev => {
        const newSet = new Set([...prev]);
        newSet.delete(cardId);
        return newSet;
      });
    }, ANIMATION_DURATION.resetProcessing);
  };

  // 处理卡牌点击事件
  const handleCardClick = (index: number) => {
    if (!dragActive && !flippedCards.includes(index)) {
      // 保存当前页面滚动位置
      const pageScrollY = window.scrollY;
      
      // 处理卡牌翻转
      handleCardFlip(index);
      
      // 确保页面滚动位置不变
      setTimeout(() => {
        if (window.scrollY !== pageScrollY) {
          window.scrollTo({
            top: pageScrollY,
            behavior: 'auto'
          });
        }
      }, 10);
    }
  };

  // 添加API调用相关状态
  const [isLoadingReading, setIsLoadingReading] = useState<boolean>(false);
  const [readingError, setReadingError] = useState<string>('');
  const [streamContent, setStreamContent] = useState<string>('');

  // 修改handleFormSpread函数
  const handleFormSpread = async () => {
    // 如果已有历史记录，不执行滚动操作
    if (hasExistingReading) {
      return;
    }
    
    if (flippedCards.length < requiredCards) return;

    // 记录当前滚动位置
    const currentScrollY = window.scrollY;

    // 准备选中的卡牌信息
    const cardsInfo = {
      ids: flippedCards,
      orientations: Array.from(cardOrientations.entries()),
      completed: true
    };
    localStorage.setItem('selectedCardsInfo', JSON.stringify(cardsInfo));

    // 准备选中的卡牌数据
    const selectedCardsData = flippedCards.map((cardId, index) => {
      const card = TAROT_CARDS[cardId];
      const isReversed = cardOrientations.get(cardId) || false;
      
      // 获取牌阵位置信息
      let position = '';
      if (translatedSpreadInfo && translatedSpreadInfo.positions && translatedSpreadInfo.positions[index]) {
        position = translatedSpreadInfo.positions[index];
      }
      
      return {
        id: cardId,
        name: card.name,
        nameEn: card.nameEn,
        isReversed: isReversed,
        orientation: isReversed ? 'reversed' : 'upright',
        position
      };
    });

    // 保存到localStorage
    localStorage.setItem('selectedCards', JSON.stringify(selectedCardsData));

    // 设置默认问题
    const defaultQuestion = 
      currentPost?.tarotSpread?.defaultQuestion || // 优先使用博客文章定义的默认问题
      (i18n.language === 'en' ? '[Question/Keyword from the article]' :
      i18n.language === 'ja' ? '[記事からの質問/キーワード]' :
      i18n.language === 'zh-TW' ? '[文章中的問題/關鍵詞]' :
      '[文章中的问题/关键词]');

    try {
      // 生成临时会话ID（仅用于前端跟踪，不发送到后端）
      const tempSessionId = `temp-${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;
      localStorage.setItem('sessionId', tempSessionId);

      // 设置已完成抽牌状态
      setHasCompletedReading(true);
      
      // 开始获取解读结果
      setIsLoadingReading(true);
      setStreamContent('');
      setReadingError('');

      // 使用非流式API获取解读结果
      const readingContent = await getBlogReading({
        question: defaultQuestion,
        selectedCards: selectedCardsData,
        selectedSpread: spread,
        sessionId: tempSessionId, // 传递临时ID
        language: i18n.language,
        fingerprint: fingerprint, // 传递指纹
        blogId: slug // 传递博客ID
      });
      
      // 设置解读内容
      setStreamContent(readingContent);
      setIsLoadingReading(false);
      
      // 恢复之前的滚动位置，防止页面自动滚动到顶部
      setTimeout(() => {
        window.scrollTo({
          top: currentScrollY,
          behavior: 'auto'
        });
      }, 50);
      
    } catch (error) {
      setIsLoadingReading(false);
      setReadingError(i18n.language === 'en' 
        ? 'An error occurred, please try again later.' 
        : '发生错误，请稍后再试。');
        
      // 错误情况下也恢复滚动位置
      setTimeout(() => {
        window.scrollTo({
          top: currentScrollY,
          behavior: 'auto'
        });
      }, 50);
    }
  };

  // 滑动卡牌左右滚动函数
  const scroll = (direction: 'left' | 'right') => {
    if (scrollRef.current) {
      const { current: container } = scrollRef;
      const scrollAmount = direction === 'left' 
        ? -container.clientWidth 
        : container.clientWidth;
      
      container.scrollBy({
        left: scrollAmount,
        behavior: 'smooth'
      });
    }
  };

  // 处理鼠标按下事件
  const handleMouseDown = (e: MouseEvent<HTMLDivElement>) => {
    if (!scrollRef.current) return;
    
    setIsDragging(true);
    setDragActive(false);
    setStartX(e.pageX - scrollRef.current.offsetLeft);
    setScrollLeft(scrollRef.current.scrollLeft);
    
    // 修改光标样式
    if (scrollRef.current) {
      scrollRef.current.style.cursor = 'grabbing';
      scrollRef.current.style.userSelect = 'none';
    }
  };

  // 处理鼠标移动事件
  const handleMouseMove = (e: MouseEvent<HTMLDivElement>) => {
    if (!isDragging || !scrollRef.current) return;
    
    // 防止选中文本
    e.preventDefault();
    
    const x = e.pageX - scrollRef.current.offsetLeft;
    const walk = (x - startX) * 2; // 滚动速度系数
    
    scrollRef.current.scrollLeft = scrollLeft - walk;
    
    // 如果拖动距离超过5像素，将dragActive设为true
    if (Math.abs(walk) > 5) {
      setDragActive(true);
    }
  };

  // 处理鼠标释放事件
  const handleMouseUp = () => {
    setIsDragging(false);
    
    // 恢复光标样式
    if (scrollRef.current) {
      scrollRef.current.style.cursor = 'grab';
      scrollRef.current.style.userSelect = 'auto';
    }
    
    // 短暂延迟后重置dragActive
    setTimeout(() => {
      setDragActive(false);
    }, 50);
  };

  // 处理鼠标离开事件
  const handleMouseLeave = () => {
    if (isDragging) {
      setIsDragging(false);
      
      // 恢复光标样式
      if (scrollRef.current) {
        scrollRef.current.style.cursor = 'grab';
        scrollRef.current.style.userSelect = 'auto';
      }
    }
  };

  // 处理数字选择
  const handleNumberSelect = () => {
    const number = parseInt(numberInputValue);
    if (isNaN(number) || number < 1 || number > 78) {
      if (numberInputErrorTimeoutRef.current) {
        clearTimeout(numberInputErrorTimeoutRef.current);
      }
      
      setNumberInputError(t('reading.shuffle.number_error.invalid_range'));
      
      numberInputErrorTimeoutRef.current = setTimeout(() => {
        setNumberInputError('');
      }, 2000);
      return;
    }
    
    // 数字输入是从1开始的，所以要减1得到索引
    const inputIndex = number - 1;
    
    // 使用输入的编号对应的位置
    let cardId;
    if (randomCardIndices.length === 78) {
      // 使用随机索引数组中的第N张牌
      cardId = randomCardIndices[inputIndex];
    } else {
      // 如果没有随机索引数组，直接使用输入索引
      cardId = inputIndex;
    }
    
    if (flippedCards.includes(cardId)) {
      if (numberInputErrorTimeoutRef.current) {
        clearTimeout(numberInputErrorTimeoutRef.current);
      }
      
      setNumberInputError(t('reading.shuffle.number_error.already_selected'));
      
      numberInputErrorTimeoutRef.current = setTimeout(() => {
        setNumberInputError('');
      }, 2000);
      return;
    }
    
    if (flippedCards.length >= requiredCards) {
      if (numberInputErrorTimeoutRef.current) {
        clearTimeout(numberInputErrorTimeoutRef.current);
      }
      
      setNumberInputError(t('reading.shuffle.number_error.enough_cards'));
      
      numberInputErrorTimeoutRef.current = setTimeout(() => {
        setNumberInputError('');
      }, 2000);
      return;
    }
    
    setNumberInputValue('');
    handleCardFlip(cardId);
  };

  
  // 提取标题并生成导航
  useEffect(() => {
    if (contentRef.current) {
      // 延迟执行以确保内容已完全渲染
      const timer = setTimeout(() => {
        // 根据showOnlyH2Headings属性决定选择哪些标题
        const selector = currentPost?.showOnlyH2Headings ? 'h2' : 'h2, h3';
        const headingElements = contentRef.current?.querySelectorAll(selector);
        
        if (headingElements) {
          const extractedHeadings = Array.from(headingElements).map((heading, index) => {
            // 为每个标题生成ID（如果没有）
            if (!heading.id) {
              heading.id = `heading-${index}`;
            }
            
            return {
              id: heading.id,
              text: heading.textContent || '',
              level: heading.tagName === 'H2' ? 2 : 3
            };
          });
          
          setHeadings(extractedHeadings);
        }
      }, 100);
      
      return () => clearTimeout(timer);
    }
  }, [contentRef, currentPost]);
  
  // 监听滚动，高亮当前可见的标题
  useEffect(() => {
    const handleScroll = () => {
      if (headings.length === 0 || !contentRef.current) return;
      
      // 获取所有标题元素的位置
      const headingPositions = headings.map(heading => {
        const element = document.getElementById(heading.id);
        if (!element) return { id: heading.id, top: 0 };
        
        const rect = element.getBoundingClientRect();
        return { id: heading.id, top: rect.top };
      });
      
      // 找出当前可见的标题（视窗顶部以下的第一个标题）
      const currentHeading = headingPositions
        .filter(h => h.top < 150) // 加一点偏移量，让标题滚动到接近顶部时就高亮
        .reduce((prev, current) => {
          return current.top > prev.top ? current : prev;
        }, { id: '', top: -Infinity });
      
      if (currentHeading.id) {
        setActiveId(currentHeading.id);
      }
    };
    
    window.addEventListener('scroll', handleScroll);
    // 初始调用一次设置初始状态
    handleScroll();
    
    return () => window.removeEventListener('scroll', handleScroll);
  }, [headings]);
  
  useEffect(() => {
    // 如果找不到文章，重定向到博客列表页
    if (!slug || !currentPost) {
      navigate('/blog');
    }
    
    // 只有在slug变化时才滚动到页面顶部，避免其他状态变化触发滚动
    if (slug) {
      // 使用sessionStorage记录已访问的文章，避免重复滚动
      const visitedArticles = JSON.parse(sessionStorage.getItem('visitedArticles') || '[]');
      if (!visitedArticles.includes(slug)) {
        window.scrollTo(0, 0);
        // 添加到已访问列表
        visitedArticles.push(slug);
        sessionStorage.setItem('visitedArticles', JSON.stringify(visitedArticles));
      }
    }
  }, [slug, currentPost, navigate]);

  // 替换动态语言链接
  useEffect(() => {
    if (contentRef.current) {
      // 查找带有特定数据属性的链接
      const languageLinks = contentRef.current.querySelectorAll('[data-dynamic-lang-link="true"]');
      
      // 为每个链接替换:lang占位符
      languageLinks.forEach(link => {
        const href = link.getAttribute('href');
        if (href && href.includes(':lang')) {
          // 替换:lang为当前语言
          const newHref = href.replace(':lang', i18n.language);
          link.setAttribute('href', newHref);
        }
      });
    }
  }, [contentRef, i18n.language, currentPost]);

  // 为深色主题添加动态样式
  useEffect(() => {
    if (theme === 'dark') {
      // 创建样式元素
      const styleEl = document.createElement('style');
      styleEl.setAttribute('id', 'dark-theme-blog-styles');
      styleEl.setAttribute('type', 'text/css');
      styleEl.innerHTML = `
        /* 表格样式适配 */
        .bg-gray-100, .bg-gray-50, .bg-[#F4F4F5] {
          background-color: rgba(31, 41, 55, 0.5) !important;
        }
        
        .tarot-comparison-table {
          border-color: #4b5563 !important;
        }
        
        .tarot-comparison-table thead tr {
          background-color: rgba(55, 65, 81, 0.7) !important;
        }
        
        .tarot-comparison-table tbody tr.bg-gray-50 {
          background-color: rgba(55, 65, 81, 0.5) !important;
        }
        
        .tarot-comparison-table tbody tr:not(.bg-gray-50) {
          background-color: rgba(31, 41, 55, 0.4) !important;
        }
        
        .tarot-comparison-table th, .tarot-comparison-table td {
          color: #e5e7eb !important;
          border-color: #4b5563 !important;
        }
        
        /* 案例对比部分样式适配 */
        .bg-gray-50.border.border-gray-200, .bg-\\[\\#F4F4F5\\].border.border-gray-200 {
          background-color: rgba(31, 41, 55, 0.5) !important;
          border-color: #4b5563 !important;
        }
        
        /* 特定针对案例分析面板 */
        .case-study-panel {
          background-color: rgba(31, 41, 55, 0.5) !important;
          border-color: #4b5563 !important;
        }
        
        .text-gray-800, .text-gray-700 {
          color: #e5e7eb !important;
        }
        
        .text-gray-600 {
          color: #d1d5db !important;
        }
      `;
      document.head.appendChild(styleEl);
      
      // 清理函数
      return () => {
        const existingStyle = document.getElementById('dark-theme-blog-styles');
        if (existingStyle) {
          document.head.removeChild(existingStyle);
        }
      };
    }
  }, [theme]);

  // 点击导航项滚动到对应位置
  const scrollToHeading = (id: string) => {
    const element = document.getElementById(id);
    if (element) {
      // 使用平滑滚动效果
      element.scrollIntoView({ behavior: 'smooth', block: 'start' });
      setActiveId(id);
    }
  };

  // 处理语言变化，但不滚动到顶部
  useEffect(() => {
    // 语言变化时更新内容，但保持当前滚动位置
    if (contentRef.current && i18n.language) {
      // 保存当前滚动位置
      const currentScrollY = window.scrollY;
      
      // 语言变化后的内容更新逻辑
      setTimeout(() => {
        // 恢复滚动位置
        window.scrollTo({
          top: currentScrollY,
          behavior: 'auto'
        });
      }, 100);
    }
  }, [i18n.language]);

  // 添加导航到阅读页面的处理函数
  const handleStartReading = () => {
    navigate('/');
  };

  if (!currentPost) {
    return null; // 正在重定向或找不到文章
  }

  return (
    <div className={`min-h-screen flex flex-col relative antialiased ${isDark ? 'text-white' : 'text-gray-800'}`}>
      {/* 添加指纹组件 */}
      <BrowserFingerprint onFingerprintReady={handleFingerprintReady} />
      
      <SEO 
        title={currentPost?.title} 
        description={
          currentPost?.description || 
          getSEOConfig(`/blog/${currentPost?.slug}`, i18n)?.description
        }
        canonical={currentPost?.useNewUrlFormat ? 
          `https://tarotqa.com${i18n.language !== 'zh-TW' ? `/${i18n.language}` : ''}/${getCategoryPath()}/${currentPost?.slug}` : 
          `https://tarotqa.com${i18n.language !== 'zh-TW' ? `/${i18n.language}` : ''}/blog/${currentPost?.slug}`
        }
        ogImage={currentPost?.coverImage}
        ogType="article"
      />
      
      <LandingBackground />
      
      <div className="flex-grow relative z-10">
        {/* 左侧导航栏(在小屏幕上隐藏) */}
        {headings.length > 0 && (
          <div className="hidden lg:block fixed left-0 top-1/2 transform -translate-y-1/2 z-20 ml-4 xl:ml-8">
            <div className={`max-w-[240px] p-4 rounded-lg ${theme === 'dark' ? 'bg-gray-900/70' : 'bg-white/80'} backdrop-blur-sm shadow-lg border ${theme === 'dark' ? 'border-gray-700' : 'border-gray-200'}`}>
              <h3 className={`text-sm font-bold mb-3 ${theme === 'dark' ? 'text-gray-200' : 'text-gray-700'}`}>
                {i18n.language === 'en' ? 'Table of Contents' : 
                 i18n.language === 'ja' ? '目次' : 
                 i18n.language === 'zh-TW' ? '目錄導航' : '目录导航'}
              </h3>
              <nav className="space-y-1.5 max-h-[calc(100vh-200px)] overflow-y-auto pr-1">
                {headings.map((heading) => (
                  <button
                    key={heading.id}
                    onClick={() => scrollToHeading(heading.id)}
                    className={`block text-left w-full px-2 py-1.5 rounded text-sm transition-colors duration-200
                      ${heading.level === 3 ? 'pl-4' : ''} 
                      ${activeId === heading.id 
                        ? (theme === 'dark' ? 'bg-purple-900/50 text-white font-medium' : 'bg-purple-100 text-purple-800 font-medium')
                        : (theme === 'dark' ? 'text-gray-300 hover:text-white hover:bg-gray-800/50' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100')
                      }`}
                  >
                    {heading.text}
                  </button>
                ))}
              </nav>
            </div>
          </div>
        )}
      
        {/* 文章内容 */}
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 pb-16 max-w-5xl">
          {/* 文章标题区 - 添加负边距减少与导航栏的间距 */}
          <div className="pt-8 sm:pt-6 -mt-20 sm:-mt-16 mb-8">
            <h1 className={`main-title mb-2 sm:mb-3 ${getFontClass(i18n.language)}`} style={{ textAlign: 'left' }}>
              {currentPost.title}
            </h1>
          </div>
          
          {/* 作者信息 */}
          <div className="flex items-center mb-8">
            {/* 作者头像和信息 */}
            <div className="flex items-center">
              <div className="w-12 h-12 rounded-full bg-purple-600 flex items-center justify-center text-white mr-4">
                <span style={{color: 'white'}}>T</span>
              </div>
              <div>
                <div className={`font-medium ${theme === 'dark' ? 'text-gray-200' : 'text-gray-800'}`}>
                  {i18n.language === 'en' ? 'TarotQA Team' : 
                   i18n.language === 'ja' ? 'TarotQAチーム' : 'TarotQA团队'}
                </div>
                <div className="flex items-center text-gray-500 text-sm">
                  <span>{currentPost.date}</span>
                  <span className="mx-2">·</span>
                  <span>
                    {i18n.language === 'en' ? '5 min read' : 
                     i18n.language === 'ja' ? '5分で読める' : 
                     i18n.language === 'zh-TW' ? '5 分鐘閱讀' : '5 分钟阅读'}
                  </span>
                </div>
              </div>
            </div>
            
            {/* 添加类别标签 */}
            {/* 删除类别标签部分
            {currentPost.category && (
              <div className="ml-auto">
                <span className={`px-3 py-1 rounded-full text-sm ${
                  theme === 'dark' 
                    ? 'bg-purple-900/50 text-purple-200' 
                    : 'bg-purple-100 text-purple-700'
                }`}>
                  {currentPost.category}
                </span>
              </div>
            )}
            */}
          </div>
          
          {/* 根据设备类型调整布局 */}
          {isMobile ? (
            <>
              {/* 移动端布局：顶部显示封面图 */}
              <CoverImage coverImage={currentPost.coverImage} title={currentPost.title} />
              
              {/* 移动端布局：作者介绍下方接文章正文 */}
              <div 
                ref={contentRef} 
                className={`prose prose-lg ${theme === 'dark' ? 'prose-invert prose-headings:text-gray-100 prose-p:text-gray-300' : 'prose-slate'} mx-0 prose-headings:mt-8 prose-headings:mb-4 max-w-none`}
                style={{ fontSize: '1rem', lineHeight: '1.65' }} // 移动端优化字体大小和行高
              >
                {currentPost.content}
              </div>
              
              {/* 文章末尾显示塔罗牌抽取功能 */}
              <div className="mt-8 pt-8 border-t border-gray-200 dark:border-gray-700">
                
                {/* 只有当博客未设置禁用抽牌时才显示抽牌组件 */}
                {!currentPost.disableTarotDrawing && (
                  <TarotDrawingSection 
                    isDark={isDark}
                    i18n={i18n}
                    t={t}
                    currentPost={currentPost}
                    numberInputError={numberInputError}
                    selectionMode={selectionMode}
                    setSelectionMode={setSelectionMode}
                    numberInputValue={numberInputValue}
                    setNumberInputValue={setNumberInputValue}
                    handleNumberSelect={handleNumberSelect}
                    theme={theme}
                    scrollRef={scrollRef}
                    randomCardIndices={randomCardIndices}
                    flippedCards={flippedCards}
                    processingCards={processingCards}
                    cardOrientations={cardOrientations}
                    cardBackImage={cardBackImage}
                    cardImages={cardImages}
                    currentLanguage={currentLanguage}
                    dragActive={dragActive}
                    ANIMATION_DURATION={ANIMATION_DURATION}
                    handleMouseDown={handleMouseDown}
                    handleMouseMove={handleMouseMove}
                    handleMouseUp={handleMouseUp}
                    handleMouseLeave={handleMouseLeave}
                    handleCardClick={handleCardClick}
                    translatedSpreadInfo={translatedSpreadInfo}
                    requiredCards={requiredCards}
                    handleFormSpread={handleFormSpread}
                    hasExistingReading={hasExistingReading}
                    isLoadingReading={isLoadingReading}
                    hasCompletedReading={hasCompletedReading}
                    scroll={scroll}
                  />
                )}
              </div>
            </>
          ) : (
            <>
              {/* 桌面端布局：保持原有顺序 */}
              <CoverImage coverImage={currentPost.coverImage} title={currentPost.title} />
              
              {/* 只有当博客未设置禁用抽牌时才显示抽牌组件 */}
              {!currentPost.disableTarotDrawing && (
                <TarotDrawingSection 
                  isDark={isDark}
                  i18n={i18n}
                  t={t}
                  currentPost={currentPost}
                  numberInputError={numberInputError}
                  selectionMode={selectionMode}
                  setSelectionMode={setSelectionMode}
                  numberInputValue={numberInputValue}
                  setNumberInputValue={setNumberInputValue}
                  handleNumberSelect={handleNumberSelect}
                  theme={theme}
                  scrollRef={scrollRef}
                  randomCardIndices={randomCardIndices}
                  flippedCards={flippedCards}
                  processingCards={processingCards}
                  cardOrientations={cardOrientations}
                  cardBackImage={cardBackImage}
                  cardImages={cardImages}
                  currentLanguage={currentLanguage}
                  dragActive={dragActive}
                  ANIMATION_DURATION={ANIMATION_DURATION}
                  handleMouseDown={handleMouseDown}
                  handleMouseMove={handleMouseMove}
                  handleMouseUp={handleMouseUp}
                  handleMouseLeave={handleMouseLeave}
                  handleCardClick={handleCardClick}
                  translatedSpreadInfo={translatedSpreadInfo}
                  requiredCards={requiredCards}
                  handleFormSpread={handleFormSpread}
                  hasExistingReading={hasExistingReading}
                  isLoadingReading={isLoadingReading}
                  hasCompletedReading={hasCompletedReading}
                  scroll={scroll}
                />
              )}
              
              <div
                ref={contentRef}
                className={`prose prose-lg ${theme === 'dark' ? 'prose-invert prose-headings:text-gray-100 prose-p:text-gray-300' : 'prose-slate'} mx-0 prose-headings:mt-8 prose-headings:mb-4 max-w-none`}
              >
                {currentPost.content}
              </div>
            </>
          )}
          
          {/* 塔罗解读结果区域 */}
          {hasCompletedReading && !currentPost.disableTarotDrawing && (
            <div className="mt-12 pt-8 border-t border-gray-200 dark:border-gray-700">
              <div className={`p-6 ${theme === 'dark' ? 'bg-gray-800/50' : 'bg-purple-50/70'} rounded-xl border ${theme === 'dark' ? 'border-purple-900/30' : 'border-purple-200'} shadow-lg`}>
                <h2 className={`text-2xl font-bold mb-6 ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
                  {i18n.language === 'en' ? 'Your Tarot Reading' : 
                   i18n.language === 'ja' ? 'あなたのタロット解読結果' : 
                   i18n.language === 'zh-TW' ? '你的塔羅解讀結果' : 
                   '你的塔罗解读结果'}
                </h2>
                
                {/* 占卜问题 */}
                <div className="mb-6">
                  <h3 className={`text-lg font-semibold mb-2 ${theme === 'dark' ? 'text-gray-200' : 'text-gray-800'}`}>
                    {i18n.language === 'en' ? 'Your Question:' : 
                     i18n.language === 'ja' ? 'あなたの質問:' : 
                     i18n.language === 'zh-TW' ? '你的問題:' : 
                     '你的问题:'}
                  </h3>
                  <p className={`${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'}`}>
                    {currentPost?.tarotSpread?.defaultQuestion}
                  </p>
                </div>
                
                {/* 选中的卡牌列表 */}
                <div className="mb-6">
                  <h3 className={`text-lg font-semibold mb-4 ${theme === 'dark' ? 'text-gray-200' : 'text-gray-800'}`}>
                    {i18n.language === 'en' ? 'Your Selected Cards:' : 
                     i18n.language === 'ja' ? '選ばれたカード:' : 
                     i18n.language === 'zh-TW' ? '你選擇的卡牌:' : 
                     '你选择的卡牌:'}
                  </h3>
                  
                  <div className="flex flex-wrap justify-center mb-6">
                    {flippedCards.map((cardId, index) => {
                      const isReversed = cardOrientations.get(cardId) || false;
                      const position = translatedSpreadInfo?.positions?.[index] || `位置 ${index + 1}`;
                      
                      // 根据卡牌数量决定单个卡牌的宽度
                      const getCardWidth = () => {
                        const cardCount = flippedCards.length;
                        if (cardCount <= 3) {
                          return "w-[110px] sm:w-[130px] md:w-[150px]"; // 少量卡牌时可以显示得大一些
                        } else if (cardCount <= 6) {
                          return "w-[100px] sm:w-[120px] md:w-[140px]"; // 中等数量卡牌
                        } else {
                          return "w-[90px] sm:w-[100px] md:w-[120px]";  // 大量卡牌时适当减小
                        }
                      };
                      
                      return (
                        <div 
                          key={index}
                          className={`${getCardWidth()} m-2 sm:m-3 md:m-4 flex flex-col items-center`}
                        >
                          <div className="relative w-full">
                            <div className={`w-full aspect-[2/3] rounded-lg overflow-hidden border-0 transition-all duration-300 
                              ${isDark ? 'shadow-black/30' : 'shadow-gray-300/70'} shadow-md
                              flex items-center justify-center bg-transparent`}
                            >
                              <div 
                                className="h-full w-full flex items-center justify-center"
                                style={{ 
                                  transform: isReversed ? 'rotate(180deg)' : 'rotate(0deg)'
                                }}
                              >
                                <CdnLazyImage 
                                  src={cardImages.get(cardId) || ''}
                                  alt={currentLanguage === 'en' ? TAROT_CARDS[cardId]?.nameEn.replace(/_/g, ' ') : TAROT_CARDS[cardId]?.name}
                                  className="h-full w-full object-contain"
                                  style={{ imageRendering: 'crisp-edges' }}
                                  draggable="false"
                                />
                              </div>
                            </div>
                          </div>
                          <div className="text-center mt-2 space-y-0.5 w-full">
                            <h3 className={`text-xs sm:text-sm font-medium ${theme === 'dark' ? 'text-white' : 'text-gray-900'} font-sans japanese truncate`}>
                              {currentLanguage === 'en' ? TAROT_CARDS[cardId]?.nameEn.replace(/_/g, ' ') : TAROT_CARDS[cardId]?.name}
                            </h3>
                            <p className={`text-xs sm:text-sm ${theme === 'dark' ? 'text-purple-400' : 'text-purple-600'} font-sans japanese`}>
                              {t(isReversed ? 'reading.result.reversed' : 'reading.result.upright')}
                            </p>
                            <p className={`text-xs sm:text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'} font-sans japanese truncate`}>
                              {position}
                            </p>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
                
                {/* 解读内容 */}
                <div>
                  <h3 className={`text-lg font-semibold mb-4 ${theme === 'dark' ? 'text-gray-200' : 'text-gray-800'}`}>
                    {i18n.language === 'en' ? 'Interpretation:' : 
                     i18n.language === 'ja' ? '解釈:' : 
                     i18n.language === 'zh-TW' ? '解讀:' : 
                     '解读:'}
                  </h3>
                  
                  {isLoadingReading && !hasExistingReading && (
                    <div className="flex flex-col items-center justify-center py-4">
                      <div className="w-10 h-10 border-3 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
                      <p className="mt-2 text-sm text-purple-500">
                        {i18n.language === 'en' ? 'Generating your reading...' : 
                         i18n.language === 'ja' ? '解読を生成中...' : 
                         i18n.language === 'zh-TW' ? '正在生成解讀...' : 
                         '正在生成解读...'}
                      </p>
                    </div>
                  )}
                  
                  {readingError && (
                    <div className="bg-red-100 border border-red-300 text-red-700 px-4 py-3 rounded mb-4">
                      {readingError}
                    </div>
                  )}
                  
                  <div className={`prose prose-lg ${theme === 'dark' ? 'prose-invert prose-p:text-gray-300' : 'prose-slate'} max-w-none`}>                    {hasExistingReading ? (                      <div className="whitespace-pre-line">                        {historicalReading}                      </div>                    ) : streamContent ? (                      <div className="whitespace-pre-line">                        {streamContent}                      </div>                    ) : (!isLoadingReading && !readingError) && (                      <p>                        {i18n.language === 'en' ?                           'Based on the cards you\'ve drawn, this spread suggests...' :                          i18n.language === 'ja' ?                           'あなたが引いたカードに基づくと、このスプレッドは...' :                          i18n.language === 'zh-TW' ?                           '根據你抽選的卡牌，這個牌陣顯示...' :                           '根据你抽选的卡牌，这个牌阵显示...'}                      </p>                    )}
                  </div>
                  
                  {/* 返回文章顶部按钮 */}
                  <div className="mt-8 text-center">
                    <button 
                      onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
                      className={`px-6 py-2 rounded-full text-sm font-medium transition-colors duration-200 
                        ${theme === 'dark' ? 'bg-purple-700 hover:bg-purple-600 text-white' : 'bg-purple-100 hover:bg-purple-200 text-purple-700'}`}
                    >
                      {i18n.language === 'en' ? 'Back to Top' : 
                       i18n.language === 'ja' ? 'トップに戻る' : 
                       i18n.language === 'zh-TW' ? '返回頂部' : 
                       '返回顶部'}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}
          
          
          {/* 作者信息卡片 */}
          <div className={`mt-10 p-6 ${theme === 'dark' ? 'bg-gray-900/60 border-gray-800' : 'bg-gray-50 border-gray-100'} rounded-xl border backdrop-blur-sm`}>
            <div className="flex items-start">
              <div className="w-16 h-16 rounded-full bg-purple-600 flex items-center justify-center text-white mr-4 flex-shrink-0">
                <span style={{color: 'white'}}>T</span>
              </div>
              <div>
                <h3 className={`text-lg font-medium mb-2 ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
                  {i18n.language === 'en' ? 'TarotQA Team' : 
                   i18n.language === 'ja' ? 'TarotQAチーム' : 
                   i18n.language === 'zh-TW' ? 'TarotQA團隊' : 'TarotQA团队'}
                </h3>
                <p className={`${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
                  {i18n.language === 'en' ? 
                    'The TarotQA team is dedicated to simplifying tarot learning, providing clear and practical guides and tools for tarot enthusiasts, making tarot reading more intuitive and effective.' : 
                   i18n.language === 'ja' ? 
                    'TarotQAチームは、タロット学習を簡素化し、タロット愛好家に明確で実用的なガイドとツールを提供し、タロットリーディングをより直感的で効果的にすることに専念しています。' : 
                   i18n.language === 'zh-TW' ? 
                    'TarotQA團隊致力於簡化塔羅學習，為塔羅愛好者提供清晰、實用的指南和工具，讓塔羅閱讀更加直觀和有效。' : 
                    'TarotQA团队致力于简化塔罗学习，为塔罗爱好者提供清晰、实用的指南和工具，让塔罗阅读更加直观和有效。'}
                </p>
              </div>
            </div>
          </div>
          
          {/* 相关文章组件 */}
          <RelatedArticles 
            currentSlug={slug || ''} 
            theme={theme} 
            language={i18n.language} 
            navigate={navigate}
            i18n={i18n}
          />
        </div>
      </div>

      {/* 添加探索区域 - 在文章内容和相关文章之间 */}
      <div className="relative z-10">
        <Suspense
          fallback={
            <div className="w-full h-[200px] bg-gray-900 rounded-lg animate-pulse" />
          }
        >
          <div className="spotlight-section py-24 md:py-32 mt-16">
            <div className="max-w-5xl mx-auto px-2 sm:px-4">
              <SpotlightCard
                className="custom-spotlight-card"
                spotlightColor="rgba(0, 229, 255, 0.2)"
              >
                <div className="p-4 sm:p-8 text-center">
                  <h3
                    className="text-2xl md:text-3xl font-semibold mb-4"
                    style={{
                      background: theme === 'light' 
                        ? "none" 
                        : "linear-gradient(135deg, #E2E8FF, #FFF1F2, #FAF5FF)",
                      WebkitBackgroundClip: theme === 'light' ? "inherit" : "text",
                      WebkitTextFillColor: theme === 'light' ? "#000" : "transparent",
                      color: theme === 'light' ? "#000" : "inherit"
                    }}
                  >
                    {t("home.explore_section.title", "探索塔罗牌阅读")}
                  </h3>
                  <p className={`${
                    theme === 'light' ? 'text-gray-700' : 'text-gray-300'
                  } text-lg md:text-xl mb-6 px-1`}>
                    {t("home.explore_section.description", "开始您的塔罗之旅，获取专属于您的塔罗牌阅读")}
                  </p>
                  <div className="flex justify-center">
                    <motion.button
                      onClick={handleStartReading}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="px-6 py-3 rounded-full"
                      style={{
                        background:
                          theme === 'light'
                            ? "linear-gradient(135deg, rgba(124, 58, 237, 0.7), rgba(168, 85, 247, 0.7))"
                            : "linear-gradient(135deg, rgba(124, 58, 237, 0.8), rgba(168, 85, 247, 0.8))",
                        boxShadow: theme === 'light' 
                          ? "0 0 20px rgba(168, 85, 247, 0.4)"
                          : "0 0 20px rgba(168, 85, 247, 0.5)",
                        color: 'white',
                      }}
                    >
                      {t("home.explore_section.button", "开始阅读")}
                    </motion.button>
                  </div>
                </div>
              </SpotlightCard>
            </div>
          </div>
        </Suspense>
      </div>

      <Footer />
      
      {/* 添加卡牌样式 */}
      <TarotCardStyles isDark={isDark} />
    </div>
  );
};

// 文章封面图组件
const CoverImage: React.FC<{
  coverImage: string;
  title: string;
}> = ({ coverImage, title }) => {
  return (
    <div className="mb-8 rounded-lg overflow-hidden">
      <CdnLazyImage 
        src={coverImage} 
        alt={title} 
        className="w-full h-auto object-cover"
      />
    </div>
  );
};

// 塔罗牌抽取功能组件
const TarotDrawingSection: React.FC<{
  isDark: boolean;
  i18n: any;
  t: any;
  currentPost: any;
  numberInputError: string;
  selectionMode: 'slide' | 'number';
  setSelectionMode: React.Dispatch<React.SetStateAction<'slide' | 'number'>>;
  numberInputValue: string;
  setNumberInputValue: React.Dispatch<React.SetStateAction<string>>;
  handleNumberSelect: () => void;
  theme: string;
  scrollRef: React.RefObject<HTMLDivElement>;
  randomCardIndices: number[];
  flippedCards: number[];
  processingCards: Set<number>;
  cardOrientations: Map<number, boolean>;
  cardBackImage: string;
  cardImages: Map<number, string>;
  currentLanguage: string;
  dragActive: boolean;
  ANIMATION_DURATION: any;
  handleMouseDown: (e: MouseEvent<HTMLDivElement>) => void;
  handleMouseMove: (e: MouseEvent<HTMLDivElement>) => void;
  handleMouseUp: () => void;
  handleMouseLeave: () => void;
  handleCardClick: (index: number) => void;
  translatedSpreadInfo: any;
  requiredCards: number;
  handleFormSpread: () => void;
  hasExistingReading: boolean;
  isLoadingReading: boolean;
  hasCompletedReading: boolean;
  scroll: (direction: 'left' | 'right') => void;
}> = ({
  isDark,
  i18n,
  t,
  currentPost,
  numberInputError,
  selectionMode,
  setSelectionMode,
  numberInputValue,
  setNumberInputValue,
  handleNumberSelect,
  theme,
  scrollRef,
  randomCardIndices,
  flippedCards,
  processingCards,
  cardOrientations,
  cardBackImage,
  cardImages,
  currentLanguage,
  dragActive,
  ANIMATION_DURATION,
  handleMouseDown,
  handleMouseMove,
  handleMouseUp,
  handleMouseLeave,
  handleCardClick,
  translatedSpreadInfo,
  requiredCards,
  handleFormSpread,
  hasExistingReading,
  isLoadingReading,
  hasCompletedReading,
  scroll
}) => {
    return (
    <div className="mb-8 pt-4 pb-8 border-b border-gray-200 dark:border-gray-700">
      <h2 className={`text-2xl font-bold mb-6 ${isDark ? 'text-white' : 'text-gray-900'}`}>
          {i18n.language === 'en' ? 'Free Online Card Drawing, Answering Your Questions!' : 
           i18n.language === 'ja' ? '無料オンラインカード占い、あなたの疑問を解決！' : 
           i18n.language === 'zh-TW' ? '免費線上抽牌，解答你的心中疑惑！' : 
           '免费在线抽牌，解答你的心中疑惑！'}
      </h2>
      
      <p className={`mb-8 prose prose-lg ${isDark ? 'prose-invert prose-p:text-gray-300' : 'prose-slate'} max-w-none`}>
        {i18n.language === 'en' ? 
          `Wondering about tarot answers to your "${currentPost?.tarotSpread?.defaultQuestion || '[Frequent Questions/Keywords]'}"? Click below for a free card draw! We've prepared a special tarot spread just for you! The draw results will be interpreted based on your question, and the interpretation will be revealed at the end of the article. No login required, completely free, let tarot provide you with immediate guidance and answers!` :
         i18n.language === 'ja' ? 
          `「${currentPost?.tarotSpread?.defaultQuestion || '[よくある質問/キーワード]'}」に関するタロットの答えが知りたいですか？下をクリックして無料でカードを引いてみましょう！あなたのために特別なタロット配置を用意しました！引いた結果はあなたの質問に基づいて解釈され、その内容は記事の最後で明らかになります。ログイン不要、完全無料で、タロットがあなたに即時のガイダンスと答えを提供します！` :
         i18n.language === 'zh-TW' ? 
          `想知道關於"${currentPost?.tarotSpread?.defaultQuestion || '[高頻問題/關鍵詞]'}"的塔羅解答嗎？立即點擊下方進行免費抽牌，我們為你準備了專屬的塔羅牌陣！抽牌結果會根據你的問題進行解讀，解讀內容將在文章末尾為你揭曉。不需要登錄，完全免費，讓塔羅為你提供即時的指引和答案！` :
          `想知道关于"${currentPost?.tarotSpread?.defaultQuestion || '[高频问题/关键词]'}"的塔罗解答吗？立即点击下方进行免费抽牌，我们为你准备了专属的塔罗牌阵！抽牌结果会根据你的问题进行解读，解读内容将在文章末尾为你揭晓。不需要登录，完全免费，让塔罗为你提供即时的指引和答案！`}
      </p>
      
      {/* 浮动提示 */}
      <FloatingHint message={numberInputError} isDark={isDark} />
      
      {/* 选牌模式切换 */}
      <div className="flex flex-col items-center justify-center gap-2 mt-4">
        <div className="flex w-full justify-center">
          <div className={`inline-flex rounded-full p-0.5 ${isDark ? 'bg-gray-800' : 'bg-gray-200'} w-full max-w-md`}>
            <button
              onClick={() => setSelectionMode('slide')}
              className={`flex-1 px-4 py-1.5 rounded-full text-sm transition-colors duration-200 ${
                selectionMode === 'slide' 
                  ? 'bg-purple-600 text-white' 
                  : `${isDark ? 'text-gray-400 hover:text-white' : 'text-gray-500 hover:text-gray-800'}`
              }`}
            >
              {t('reading.shuffle.selection_mode.slide')}
            </button>
            <button
              onClick={() => setSelectionMode('number')}
              className={`flex-1 px-4 py-1.5 rounded-full text-sm transition-colors duration-200 ${
                selectionMode === 'number' 
                  ? 'bg-purple-600 text-white' 
                  : `${isDark ? 'text-gray-400 hover:text-white' : 'text-gray-500 hover:text-gray-800'}`
              }`}
            >
              {t('reading.shuffle.selection_mode.number')}
            </button>
          </div>
        </div>
      </div>
      
      {/* 滑动浏览组件 */}
      {selectionMode === 'slide' && (
        <SlideControls 
          onScroll={scroll}
          isDark={isDark}
          t={t}
        />
      )}

      {/* 数字选牌组件 */}
      {selectionMode === 'number' && (
        <NumberInput 
          value={numberInputValue}
          onChange={setNumberInputValue}
          onSubmit={handleNumberSelect}
          theme={theme}
          t={t}
        />
      )}
      
      {/* 卡牌选择区域 */}
      <CardDeck 
        scrollRef={scrollRef}
        isDark={isDark}
        randomCardIndices={randomCardIndices}
        flippedCards={flippedCards}
        processingCards={processingCards}
        cardOrientations={cardOrientations}
        cardBackImage={cardBackImage}
        cardImages={cardImages}
        currentLanguage={currentLanguage}
        dragActive={dragActive}
        animationDuration={ANIMATION_DURATION}
        handleMouseDown={handleMouseDown}
        handleMouseMove={handleMouseMove}
        handleMouseUp={handleMouseUp}
        handleMouseLeave={handleMouseLeave}
        handleCardClick={handleCardClick}
        t={t}
      />
      
      {/* 牌阵显示区 - 默认显示 */}
      <CustomSpreadContainer 
        isDark={isDark}
        t={t}
        translatedSpreadInfo={translatedSpreadInfo}
        requiredCards={requiredCards}
        flippedCards={flippedCards}
        cardOrientations={cardOrientations}
        cardImages={cardImages}
        currentLanguage={currentLanguage}
        handleFormSpread={handleFormSpread}
        hasExistingReading={hasExistingReading}
        isLoadingReading={isLoadingReading}
        hasCompletedReading={hasCompletedReading}
      />
      
      {/* 塔罗解读提示信息 */}
      {hasCompletedReading && (
        <div className={`mt-6 p-4 rounded-lg flex items-center gap-3 border ${isDark ? 'bg-purple-900/20 border-purple-700/30 text-purple-200' : 'bg-purple-50 border-purple-200 text-purple-700'}`}>
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <p className="text-sm md:text-base">
            {hasExistingReading 
              ? (i18n.language === 'en' ? 'You have already completed a reading for this article. Scroll down to view your interpretation.' : 
                i18n.language === 'ja' ? 'このブログの解読は既に完了しています。下にスクロールして解読結果をご覧ください。' : 
                i18n.language === 'zh-TW' ? '您已經完成過本文章的塔羅解讀。請向下滾動查看解讀結果。' : 
                '您已经完成过本文章的塔罗解读。请向下滚动查看解读结果。')
              : (i18n.language === 'en' ? 'Your tarot reading is being prepared. Scroll to the end of the article to view your interpretation.' : 
                i18n.language === 'ja' ? 'タロットの解読を準備しています。記事の最後までスクロールして解読を確認してください。' : 
                i18n.language === 'zh-TW' ? '塔羅解讀正在準備中，瀏覽至文章末尾即可查看解讀' : 
                '塔罗解读正在准备中，浏览至文章末尾即可查看解读')
            }
          </p>
        </div>
      )}
    </div>
  );
};

// 添加卡牌样式
const TarotCardStyles: React.FC<{isDark: boolean}> = ({ isDark }) => {
  return (
    <style dangerouslySetInnerHTML={{ __html: `
      .perspective-1000 {
        perspective: 1000px;
      }
      .preserve-3d {
        transform-style: preserve-3d;
        will-change: transform;
      }
      .backface-hidden {
        backface-visibility: hidden;
        -webkit-backface-visibility: hidden;
      }
      /* 移除数字输入框的上下箭头 */
      input[type="number"]::-webkit-outer-spin-button,
      input[type="number"]::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
      }
      input[type="number"] {
        -moz-appearance: textfield;
        appearance: textfield;
      }
      .card-wrapper {
        transition: transform 150ms cubic-bezier(0.25, 1, 0.5, 1);
        will-change: transform, z-index;
        z-index: 1;
        transition-property: transform, z-index;
      }
      .hoverable-card:hover {
        transform: translateY(-25px);
        z-index: 100;
        transition: transform 150ms cubic-bezier(0.25, 1, 0.5, 1), z-index 0ms;
      }
      .hoverable-card {
        transition: transform 150ms cubic-bezier(0.25, 1, 0.5, 1), z-index 0ms;
        pointer-events: auto !important;
        cursor: pointer;
      }
      .flipped-card {
        transform: translateY(-25px);
        z-index: 200;
        transition: transform 150ms cubic-bezier(0.25, 1, 0.5, 1) !important;
      }
      .card-hover {
        pointer-events: auto;
      }
      .card-container {
        transform: translateZ(0);
        backface-visibility: hidden;
        -webkit-backface-visibility: hidden;
        will-change: transform;
      }
      .will-change-transform {
        will-change: transform;
      }
      .hide-scrollbar-default {
        scrollbar-width: thin;
        scrollbar-color: rgba(147, 51, 234, 0.5) ${isDark ? 'rgba(31, 41, 55, 0.3)' : 'rgba(229, 231, 235, 0.3)'};
      }
      .hide-scrollbar-default::-webkit-scrollbar {
        display: block;
        height: 8px;
      }
      .hide-scrollbar-default::-webkit-scrollbar-track {
        background: ${isDark ? 'rgba(31, 41, 55, 0.3)' : 'rgba(229, 231, 235, 0.3)'};
        border-radius: 4px;
      }
      .hide-scrollbar-default::-webkit-scrollbar-thumb {
        background: rgba(147, 51, 234, 0.5);
        border-radius: 4px;
      }
      .hide-scrollbar-default::-webkit-scrollbar-thumb:hover {
        background: rgba(147, 51, 234, 0.7);
      }
      @media (hover: none) {
        .hide-scrollbar-default {
          -webkit-overflow-scrolling: touch;
        }
        .hide-scrollbar-default::-webkit-scrollbar {
          height: 4px;
        }
      }
    `}} />
  );
};

export default BlogDetail;